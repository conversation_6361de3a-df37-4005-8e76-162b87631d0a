﻿<Project Sdk="Microsoft.NET.Sdk">
  <!-- Look at Directory.Build.props in root for common stuff as well -->
  <Import Project="..\..\Common.Dotnet.CsWinRT.props" />
  <Import Project="..\..\Common.Dotnet.AotCompatibility.props" />

  <PropertyGroup>
    <Description>PowerToys ManagedCommon</Description>
    <AssemblyName>PowerToys.ManagedCommon</AssemblyName>
  </PropertyGroup>

  <!-- See https://learn.microsoft.com/windows/apps/develop/platform/csharp-winrt/net-projection-from-cppwinrt-component for more info -->
  <PropertyGroup>
    <CsWinRTIncludes>PowerToys.Interop</CsWinRTIncludes>
    <CsWinRTGeneratedFilesDir>$(OutDir)</CsWinRTGeneratedFilesDir>
    <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Management" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\interop\PowerToys.Interop.vcxproj" />
    <ProjectReference Include="..\ManagedTelemetry\Telemetry\ManagedTelemetry.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="CommonResources.Designer.cs">
      <DependentUpon>CommonResources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="CommonResources.resx">
      <SubType>Designer</SubType>
      <LastGenOutput>CommonResources.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
  </ItemGroup>
</Project>

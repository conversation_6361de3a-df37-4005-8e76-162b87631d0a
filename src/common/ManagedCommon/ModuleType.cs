﻿// Copyright (c) Microsoft Corporation
// The Microsoft Corporation licenses this file to you under the MIT license.
// See the LICENSE file in the project root for more information.

namespace ManagedCommon
{
    public enum ModuleType
    {
        AdvancedPaste,
        AlwaysOnTop,
        Awake,
        ColorPicker,
        CmdPal,
        CropAndLock,
        EnvironmentVariables,
        FancyZones,
        FileLocksmith,
        FindMyMouse,
        Hosts,
        ImageResizer,
        KeyboardManager,
        MouseHighlighter,
        MouseJump,
        MousePointerCrosshairs,
        MouseWithoutBorders,
        NewPlus,
        Peek,
        PowerRename,
        PowerLauncher,
        PowerAccent,
        RegistryPreview,
        MeasureTool,
        ShortcutGuide,
        PowerOCR,
        Workspaces,
        ZoomIt,
    }
}

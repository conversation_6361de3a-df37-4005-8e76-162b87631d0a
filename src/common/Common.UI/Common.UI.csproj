﻿<Project Sdk="Microsoft.NET.Sdk">
  <!-- Look at Directory.Build.props in root for common stuff as well -->
  <Import Project="..\..\Common.Dotnet.CsWinRT.props" />
  <Import Project="..\..\Common.Dotnet.AotCompatibility.props" />

  <PropertyGroup>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyName>PowerToys.Common.UI</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ControlzEx" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ManagedCommon\ManagedCommon.csproj" />
  </ItemGroup>


</Project>

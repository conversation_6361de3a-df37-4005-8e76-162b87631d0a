<Project Sdk="Microsoft.NET.Sdk">
  <!-- Look at Directory.Build.props in root for common stuff as well -->
  <Import Project="..\..\Common.Dotnet.CsWinRT.props" />

  <PropertyGroup>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <TargetName>PowerToys.GPOWrapperProjection</TargetName>
    <RootNamespace>PowerToys.GPOWrapperProjection</RootNamespace>
    <AssemblyName>PowerToys.GPOWrapperProjection</AssemblyName>
  </PropertyGroup>

  <!-- See https://learn.microsoft.com/windows/apps/develop/platform/csharp-winrt/net-projection-from-cppwinrt-component for more info -->
  <PropertyGroup>
    <CsWinRTIncludes>PowerToys.GPOWrapper</CsWinRTIncludes>
    <CsWinRTGeneratedFilesDir>$(OutDir)</CsWinRTGeneratedFilesDir>
    <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\GPOWrapper\GPOWrapper.vcxproj" />
  </ItemGroup>

</Project>

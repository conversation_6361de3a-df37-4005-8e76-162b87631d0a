#include <windows.h>
#include "resource.h"
#include "../../../common/version/version.h"

#define APSTUDIO_READONLY_SYMBOLS
#include "winres.h"
#undef APSTUDIO_READONLY_SYMBOLS

1 VERSIONINFO
FILEVERSION FILE_VERSION
PRODUCTVERSION PRODUCT_VERSION
FILEFLAGSMASK VS_FFI_FILEFLAGSMASK
#ifdef _DEBUG
FILEFLAGS VS_FF_DEBUG
#else
FILEFLAGS 0x0L
#endif
FILEOS VOS_NT_WINDOWS32
FILETYPE VFT_DLL
FILESUBTYPE VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0" // US English (0x0409), Unicode (0x04B0) charset
        BEGIN
            VALUE "CompanyName", COMPANY_NAME
            VALUE "FileDescription", FILE_DESCRIPTION
            VALUE "FileVersion", FILE_VERSION_STRING
            VALUE "InternalName", INTERNAL_NAME
            VALUE "LegalCopyright", COPYRIGHT_NOTE
            VALUE "OriginalFilename", ORIGINAL_FILENAME
            VALUE "ProductName", PRODUCT_NAME
            VALUE "ProductVersion", PRODUCT_VERSION_STRING
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200 // US English (0x0409), Unicode (1200) charset
    END
END
